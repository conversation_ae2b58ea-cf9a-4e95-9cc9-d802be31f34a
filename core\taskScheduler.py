import datetime

from core.service import recordService
from flask_apscheduler import APScheduler
 
 
scheduler = APScheduler()
recordSrv = recordService()
 
# interval example, 间隔执行, 每30秒执行一次
# @scheduler.task('interval', id='do_job_1', seconds=30, misfire_grace_time=900)
# cron examples, 每分钟执行一次
# @scheduler.task('cron', id='do_job_2', minute='*')
# 每周执行一次
# @scheduler.task('cron', id='do_job_3', week='*', day_of_week='sun')
@scheduler.task('cron', id='clear_expire_records', hour='01', minute='00')
def clear_expire_records():
    # 删除3天前的数据
    deadline = datetime.datetime.today().date() - datetime.timedelta(days=2)

    delCount = 0
    for i in range(1000):
        request_id_list, count = recordSrv.getRecordsByMaxtimeId(deadline, 200)
        if not request_id_list:
            break
        request_id_list = list(map(lambda x: x.request_id, request_id_list))
        recordSrv.delRecordsByRequestId(request_id_list)
        recordSrv.delRecordsDetailByRequestId(request_id_list)
        delCount += len(request_id_list)

    print('删除数据总数: {}'.format(str(delCount)))
