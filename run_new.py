#!/usr/bin/env python3
"""
FastAPI应用启动器 - 新版本
支持多种启动模式和环境检测
"""

import os
import sys
import platform
import subprocess
import argparse
from pathlib import Path

class FastAPIRunner:
    """FastAPI应用启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system = platform.system()
        self.python_version = sys.version_info
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        required_packages = [
            'fastapi',
            'uvicorn',
            'requests',
            'pymysql'
        ]
        
        missing_packages = []
        installed_packages = {}
        
        for package in required_packages:
            try:
                module = __import__(package)
                if hasattr(module, '__version__'):
                    installed_packages[package] = module.__version__
                else:
                    installed_packages[package] = 'unknown'
            except ImportError:
                missing_packages.append(package)
        
        return installed_packages, missing_packages
    
    def print_system_info(self):
        """打印系统信息"""
        print("=" * 70)
        print("🚀 FastAPI应用启动器")
        print("=" * 70)
        print(f"📁 项目目录: {self.project_root}")
        print(f"💻 操作系统: {self.system} {platform.release()}")
        print(f"🐍 Python版本: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"📦 架构: {platform.machine()}")
        
    def print_dependencies(self):
        """打印依赖信息"""
        installed, missing = self.check_dependencies()
        
        print("\n📦 依赖检查:")
        print("-" * 40)
        
        if installed:
            for package, version in installed.items():
                print(f"✅ {package}: {version}")
        
        if missing:
            print(f"\n❌ 缺失依赖: {', '.join(missing)}")
            print("💡 安装命令: pip install -r requirements.txt")
            return False
        
        return True
    
    def check_port(self, port=9083):
        """检查端口是否被占用"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                return result != 0  # 返回True表示端口可用
        except Exception:
            return True
    
    def run_development(self, reload=True, port=9083):
        """开发模式启动"""
        print(f"\n🔧 开发模式启动 (端口: {port})")
        print("-" * 40)
        
        if not self.check_port(port):
            print(f"⚠️  端口 {port} 已被占用")
            return False
        
        try:
            import uvicorn
            print("📝 启动参数:")
            print(f"   - 应用: main:app")
            print(f"   - 地址: 0.0.0.0:{port}")
            print(f"   - 重载: {reload}")
            print(f"   - 日志级别: info")
            print("\n🌐 访问地址:")
            print(f"   - 主页: http://localhost:{port}")
            print(f"   - API文档: http://localhost:{port}/docs")
            print(f"   - 健康检查: http://localhost:{port}/health")
            print("\n" + "=" * 70)
            
            uvicorn.run(
                "main:app",
                host="0.0.0.0",
                port=port,
                reload=reload,
                log_level="info"
            )
            
        except KeyboardInterrupt:
            print("\n\n👋 应用已停止")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            return False
        
        return True
    
    def run_production_windows(self, port=9083):
        """Windows生产模式启动"""
        print(f"\n🪟 Windows生产模式启动 (端口: {port})")
        print("-" * 40)
        
        if not self.check_port(port):
            print(f"⚠️  端口 {port} 已被占用")
            return False
        
        try:
            # 使用start_windows.py
            if (self.project_root / "start_windows.py").exists():
                print("📝 使用 start_windows.py 启动")
                subprocess.run([sys.executable, "start_windows.py"], cwd=self.project_root)
            else:
                # 直接使用uvicorn
                import uvicorn
                print("📝 直接使用 uvicorn 启动")
                uvicorn.run(
                    "main:app",
                    host="0.0.0.0",
                    port=port,
                    workers=1,
                    log_level="info"
                )
                
        except KeyboardInterrupt:
            print("\n\n👋 应用已停止")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            return False
        
        return True
    
    def run_production_linux(self, port=9083, workers=5):
        """Linux生产模式启动"""
        print(f"\n🐧 Linux生产模式启动 (端口: {port})")
        print("-" * 40)
        
        if not self.check_port(port):
            print(f"⚠️  端口 {port} 已被占用")
            return False
        
        try:
            # 检查gunicorn是否可用
            try:
                import gunicorn
                print(f"📝 使用 Gunicorn {gunicorn.__version__}")
                
                # 使用gunicorn配置文件
                if (self.project_root / "gunicorn.conf.py").exists():
                    print("📝 使用 gunicorn.conf.py 配置")
                    subprocess.run([
                        "gunicorn", "main:app", 
                        "-c", "gunicorn.conf.py"
                    ], cwd=self.project_root)
                else:
                    # 直接使用gunicorn
                    subprocess.run([
                        "gunicorn", "main:app",
                        "--bind", f"0.0.0.0:{port}",
                        "--workers", str(workers),
                        "--worker-class", "uvicorn.workers.UvicornWorker",
                        "--log-level", "info"
                    ], cwd=self.project_root)
                    
            except ImportError:
                print("⚠️  Gunicorn未安装，使用 uvicorn")
                import uvicorn
                uvicorn.run(
                    "main:app",
                    host="0.0.0.0",
                    port=port,
                    log_level="info"
                )
                
        except KeyboardInterrupt:
            print("\n\n👋 应用已停止")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            return False
        
        return True
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 使用说明:")
        print("-" * 40)
        print("🔧 开发模式:")
        print("   python run_new.py --dev")
        print("   python run_new.py --dev --port 8080")
        print("   python run_new.py --dev --no-reload")
        print("\n🚀 生产模式:")
        print("   python run_new.py --prod")
        print("   python run_new.py --prod --port 8080")
        print("   python run_new.py --prod --workers 3")
        print("\n🔍 其他:")
        print("   python run_new.py --check    # 仅检查环境")
        print("   python run_new.py --help     # 显示帮助")
    
    def run(self, args):
        """主运行方法"""
        # 切换到项目目录
        os.chdir(self.project_root)
        
        # 打印系统信息
        self.print_system_info()
        
        # 检查依赖
        if not self.print_dependencies():
            return False
        
        # 仅检查环境
        if args.check:
            print("\n✅ 环境检查完成")
            return True
        
        # 显示帮助
        if args.help_mode:
            self.show_help()
            return True
        
        # 开发模式
        if args.dev:
            return self.run_development(
                reload=not args.no_reload,
                port=args.port
            )
        
        # 生产模式
        if args.prod:
            if self.system == "Windows":
                return self.run_production_windows(port=args.port)
            else:
                return self.run_production_linux(
                    port=args.port,
                    workers=args.workers
                )
        
        # 默认：根据系统自动选择
        print(f"\n🤖 自动模式 ({self.system})")
        if self.system == "Windows":
            return self.run_production_windows(port=args.port)
        else:
            return self.run_development(port=args.port)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="FastAPI应用启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--dev", action="store_true", help="开发模式启动")
    parser.add_argument("--prod", action="store_true", help="生产模式启动")
    parser.add_argument("--port", type=int, default=9083, help="端口号 (默认: 9083)")
    parser.add_argument("--workers", type=int, default=5, help="工作进程数 (默认: 5)")
    parser.add_argument("--no-reload", action="store_true", help="禁用自动重载")
    parser.add_argument("--check", action="store_true", help="仅检查环境")
    parser.add_argument("--help-mode", action="store_true", help="显示详细帮助")
    
    args = parser.parse_args()
    
    # 创建启动器实例
    runner = FastAPIRunner()
    
    try:
        success = runner.run(args)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
