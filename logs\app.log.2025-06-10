2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/jquery.min.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/jquery.min.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/css/layui.css? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/css/layui-theme-dark.css? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/layui/css/layui.css? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/css/common.css? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/css/common.css? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/md/github-markdown.min.css? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/layui.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/md/github-markdown.min.css? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/bignumber.min.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/layui/layui.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/jsonlint.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/bignumber.min.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/jsonlint.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/jquery.json.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/md/marked.min.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/jquery.json.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/md/marked.min.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/templates.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/toolbar.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/templates.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/sidebar.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/toolbar.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/sidebar.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/layout.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/order.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/layout.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/goods.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/order.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/goods.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/polymsg.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/polymsg.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/common-module.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/record.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/common-module.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/guide.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/record.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/app.js? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/modules/guide.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/app.js? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/plat/getall? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/plat/getall? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/common/getapi? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/common/getapi? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [GET]/static/favicon.ico? -- (from=192.168.5.235)
2025-06-10 15:19:16 | INFO  | server.py      :after_request        | [GET]/static/favicon.ico? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:16 | INFO  | server.py      :pre_process          | [POST]/order/get? -- (from=192.168.5.235)
2025-06-10 15:19:29 | INFO  | server.py      :after_request        | [POST]/order/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:16)
2025-06-10 15:19:29 | INFO  | server.py      :pre_process          | [POST]/order/getById? -- (from=192.168.5.235)
2025-06-10 15:19:29 | INFO  | server.py      :after_request        | [POST]/order/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:29)
2025-06-10 15:20:23 | INFO  | server.py      :pre_process          | [POST]/order/get? -- (from=192.168.5.235)
2025-06-10 15:20:30 | INFO  | server.py      :after_request        | [POST]/order/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:23)
2025-06-10 15:20:30 | INFO  | server.py      :pre_process          | [POST]/order/getById? -- (from=192.168.5.235)
2025-06-10 15:20:30 | INFO  | server.py      :after_request        | [POST]/order/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:30)
2025-06-10 15:23:45 | INFO  | server.py      :pre_process          | [POST]/order/get? -- (from=192.168.5.235)
2025-06-10 15:23:48 | INFO  | server.py      :after_request        | [POST]/order/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:45)
2025-06-10 15:23:48 | INFO  | server.py      :pre_process          | [POST]/order/getById? -- (from=192.168.5.235)
2025-06-10 15:23:48 | INFO  | server.py      :after_request        | [POST]/order/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:48)
2025-06-10 15:23:56 | INFO  | server.py      :pre_process          | [POST]/goods/get? -- (from=192.168.5.235)
2025-06-10 15:23:56 | INFO  | server.py      :after_request        | [POST]/goods/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:56)
2025-06-10 15:23:56 | INFO  | server.py      :pre_process          | [POST]/goods/getById? -- (from=192.168.5.235)
2025-06-10 15:23:56 | INFO  | server.py      :after_request        | [POST]/goods/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:56)
2025-06-10 15:23:58 | INFO  | server.py      :pre_process          | [POST]/polymsg/getApiByType? -- (from=192.168.5.235)
2025-06-10 15:23:58 | INFO  | server.py      :after_request        | [POST]/polymsg/getApiByType? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:58)
2025-06-10 15:23:58 | INFO  | server.py      :pre_process          | [POST]/polymsg/getById? -- (from=192.168.5.235)
2025-06-10 15:23:58 | INFO  | server.py      :after_request        | [POST]/polymsg/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:58)
2025-06-10 15:23:59 | INFO  | server.py      :pre_process          | [POST]/common/get? -- (from=192.168.5.235)
2025-06-10 15:23:59 | INFO  | server.py      :after_request        | [POST]/common/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:59)
2025-06-10 15:23:59 | INFO  | server.py      :pre_process          | [POST]/common/getById? -- (from=192.168.5.235)
2025-06-10 15:23:59 | INFO  | server.py      :after_request        | [POST]/common/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:59)
2025-06-10 15:24:00 | INFO  | server.py      :pre_process          | [POST]/record/get? -- (from=192.168.5.235)
2025-06-10 15:24:00 | INFO  | server.py      :after_request        | [POST]/record/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:00)
2025-06-10 15:24:02 | INFO  | server.py      :pre_process          | [GET]/static/doc/guide.md? -- (from=192.168.5.235)
2025-06-10 15:24:02 | INFO  | server.py      :after_request        | [GET]/static/doc/guide.md? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:02)
2025-06-10 15:24:03 | INFO  | server.py      :pre_process          | [POST]/order/get? -- (from=192.168.5.235)
2025-06-10 15:24:03 | INFO  | server.py      :after_request        | [POST]/order/get? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:03)
2025-06-10 15:24:03 | INFO  | server.py      :pre_process          | [POST]/order/getById? -- (from=192.168.5.235)
2025-06-10 15:24:03 | INFO  | server.py      :after_request        | [POST]/order/getById? -- 200 OK (from=192.168.5.235,2025-06-10 15:03:03)
2025-06-10 17:38:00 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 17:38:00 | INFO  | base.py        :start                | Scheduler started
2025-06-10 17:38:00 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-10 17:44:40 | INFO  | main.py        :lifespan             | 日志系统初始化完成
2025-06-10 17:44:40 | INFO  | main.py        :init_services        | 所有服务初始化完成
2025-06-10 17:44:40 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 17:44:40 | INFO  | base.py        :start                | Scheduler started
2025-06-10 17:44:40 | INFO  | main.py        :lifespan             | 定时任务已启动
2025-06-10 17:46:50 | INFO  | main.py        :startup_event        | 日志系统初始化完成
2025-06-10 17:46:50 | INFO  | main.py        :init_services        | 所有服务初始化完成
2025-06-10 17:46:50 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 17:46:50 | INFO  | base.py        :start                | Scheduler started
2025-06-10 17:46:50 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-10 17:48:30 | INFO  | main_simple.py :startup_event        | 日志系统初始化完成
2025-06-10 17:48:31 | INFO  | main_simple.py :init_services        | 所有服务初始化完成
2025-06-10 17:49:34 | INFO  | main_minimal.py:<module>             | 日志系统初始化完成
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-10 17:49:57) - 0.089s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jquery.min.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/layui.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/css/layui.css? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/css/common.css? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.005s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/md/github-markdown.min.css? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/bignumber.min.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.006s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.006s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.007s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.007s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.007s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jsonlint.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jquery.json.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.007s
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/md/marked.min.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/templates.js? -- (from=192.168.5.235)
2025-06-10 17:49:57 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/toolbar.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/sidebar.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.005s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/templates.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.005s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:57) - 0.007s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layout.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/order.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/goods.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/polymsg.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/common-module.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/record.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.005s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.007s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.007s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/guide.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/app.js? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.008s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.005s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.006s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/plat/getall? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-10 17:49:58) - 0.226s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/common/getapi? -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-10 17:49:58) - 0.009s
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-10 17:49:58 | INFO  | main_minimal.py:log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-10 17:49:58) - 0.001s
2025-06-10 17:50:26 | INFO  | main_minimal.py:log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-10 17:50:26 | INFO  | main_minimal.py:log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-10 17:50:26) - 0.001s
2025-06-10 17:50:34 | INFO  | main_minimal.py:log_requests         | [GET]/test? -- (from=127.0.0.1)
2025-06-10 17:50:34 | INFO  | main_minimal.py:log_requests         | [GET]/test? -- 200 (from=127.0.0.1,2025-06-10 17:50:34) - 0.001s
2025-06-10 17:50:42 | INFO  | main_minimal.py:log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-10 17:50:42 | INFO  | main_minimal.py:log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-10 17:50:42) - 0.005s
2025-06-10 17:54:09 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-10 17:55:08 | INFO  | main.py        :log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-10 17:55:08 | INFO  | main.py        :log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-10 17:55:08) - 0.001s
2025-06-10 17:55:16 | INFO  | main.py        :log_requests         | [GET]/order/get? -- (from=127.0.0.1)
2025-06-10 17:55:16 | INFO  | main.py        :log_requests         | [GET]/order/get? -- 200 (from=127.0.0.1,2025-06-10 17:55:16) - 0.212s
2025-06-10 17:57:37 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-10 17:57:37 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-10 17:57:37 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-10 17:57:38 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-10 17:57:38 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 17:57:38 | INFO  | base.py        :start                | Scheduler started
2025-06-10 17:57:38 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-10 17:57:38 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-10 17:58:04 | INFO  | main.py        :log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-10 17:58:04 | INFO  | main.py        :log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-10 17:58:04) - 0.001s
2025-06-10 17:59:04 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-10 17:59:05 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-10 17:59:05 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-10 17:59:05 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-10 17:59:05 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 17:59:05 | INFO  | base.py        :start                | Scheduler started
2025-06-10 17:59:05 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-10 17:59:05 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-10 17:59:31 | INFO  | main.py        :log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-10 17:59:31 | INFO  | main.py        :log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-10 17:59:31) - 0.001s
2025-06-10 17:59:38 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-10 17:59:38 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-10 17:59:38) - 0.006s
2025-06-10 18:14:19 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-10 18:14:19 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-10 18:14:19 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-10 18:14:19 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-10 18:14:19 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-10 18:14:19 | INFO  | base.py        :start                | Scheduler started
2025-06-10 18:14:19 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-10 18:14:19 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-10 18:14:40 | INFO  | main.py        :log_requests         | [GET]/docs? -- (from=127.0.0.1)
2025-06-10 18:14:40 | INFO  | main.py        :log_requests         | [GET]/docs? -- 200 (from=127.0.0.1,2025-06-10 18:14:40) - 0.002s
2025-06-10 18:14:41 | INFO  | main.py        :log_requests         | [GET]/openapi.json? -- (from=127.0.0.1)
2025-06-10 18:14:41 | INFO  | main.py        :log_requests         | [GET]/openapi.json? -- 200 (from=127.0.0.1,2025-06-10 18:14:41) - 0.015s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.064s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.006s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.002s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.013s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.017s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.004s
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- (from=127.0.0.1)
2025-06-10 18:14:53 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=127.0.0.1,2025-06-10 18:14:53) - 0.003s
2025-06-10 18:14:55 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- (from=127.0.0.1)
2025-06-10 18:14:55 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=127.0.0.1,2025-06-10 18:14:55) - 0.008s
2025-06-10 18:14:55 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- (from=127.0.0.1)
2025-06-10 18:14:55 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=127.0.0.1,2025-06-10 18:14:55) - 0.009s
2025-06-10 18:14:56 | INFO  | main.py        :log_requests         | [POST]/common/get? -- (from=127.0.0.1)
2025-06-10 18:14:56 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=127.0.0.1,2025-06-10 18:14:56) - 0.007s
2025-06-10 18:14:56 | INFO  | main.py        :log_requests         | [POST]/record/get? -- (from=127.0.0.1)
2025-06-10 18:14:56 | INFO  | main.py        :log_requests         | [POST]/record/get? -- 200 (from=127.0.0.1,2025-06-10 18:14:56) - 0.012s
2025-06-10 18:14:57 | INFO  | main.py        :log_requests         | [GET]/static/doc/guide.md? -- (from=127.0.0.1)
2025-06-10 18:14:57 | INFO  | main.py        :log_requests         | [GET]/static/doc/guide.md? -- 200 (from=127.0.0.1,2025-06-10 18:14:57) - 0.002s
2025-06-10 18:14:58 | INFO  | main.py        :log_requests         | [POST]/record/get? -- (from=127.0.0.1)
2025-06-10 18:14:58 | INFO  | main.py        :log_requests         | [POST]/record/get? -- 200 (from=127.0.0.1,2025-06-10 18:14:58) - 0.008s
2025-06-10 18:30:03 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-10 18:30:03 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-10 18:30:03) - 0.012s
