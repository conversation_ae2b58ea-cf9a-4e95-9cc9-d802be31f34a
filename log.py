import logging
import os

from flask.logging import default_handler
from logging.handlers import TimedRotatingFileHandler
from logging import StreamHandler

# 当前目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# 日志目录
LOG_PATH = os.path.join(BASE_DIR, 'logs')
LOG_PATH_ALL = os.path.join(LOG_PATH, 'app.log')


class Logger(object):

    def init_app(self, app):

        # 禁用所有相关logger的默认配置
        loggers = [
            app.logger,
            logging.getLogger('werkzeug'),
            logging.getLogger('flask._internal')  # 添加这个来控制内部logger
        ]
        
        for logger in loggers:
            logger.handlers.clear()  # 清除所有handlers
            logger.propagate = False  # 禁止传播
            logger.setLevel(logging.ERROR)  # 设置为ERROR级别，只记录错误

        # 移除默认的handler
        app.logger.removeHandler(default_handler)

        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-5s | %(filename)-15s:%(funcName)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        # 将日志输出到文件
        file_handler = TimedRotatingFileHandler(
            LOG_PATH_ALL,
            when='midnight', 
            interval=1, 
            backupCount=7,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)

        # 配置控制台处理器
        stream_handler = StreamHandler()
        stream_handler.setFormatter(formatter)
        stream_handler.setLevel(logging.INFO)

         # 只配置 Flask app logger
        app.logger.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.addHandler(stream_handler)
        
