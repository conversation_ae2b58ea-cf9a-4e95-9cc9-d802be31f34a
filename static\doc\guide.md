## 模拟网关使用说明



请求地址 `http://192.168.99.40:9083/openapi/do` 可实现和**菠萝派网关**相同的效果，默认返回成功



### **扩展参数：**

### **fail=xxx** 

举例： `http://192.168.99.40:9083/openapi/do?fail=模拟请求失败`，接口将返回失败，失败信息“模拟请求失败”



### **tag=xxx**

举例： `http://192.168.99.40:9083/openapi/do?tag=api`，用于**抓单**、**下载商品**、[**通用**]菜单中配置好的接口，将返回相应界面中**标签**=api的内容



### **sleep=xxx**

举例： `http://192.168.99.40:9083/openapi/do?sleep=1`，sleep值**不为空且不等于0**时，接口返回前，将随机休眠0.1~3秒

