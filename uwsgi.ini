[uwsgi]
# 配置启动的服务地址和ip
http=0.0.0.0:9083
# 项目目录
chdir= /root/opt/simulator-python-web
# 启动uwsgi的用户名和用户组
uid=root
gid=root
#flask项目必须的配置
callable= app
#指定虚拟环境路径，这个就是python环境的路径
#home=/root/opt/simulator-python-web

# 指定项目启动文件,该路径是相对于 chdir目录来的
wsgi-file = server.py

# 启用主进程
master=true
# 进程个数
workers=5
# 自动移除unix Socket和pid文件当服务停止的时候
vacuum=true
# 序列化接受的内容，如果可能的话
thunder-lock=true
# 启用线程
enable-threads=true
# 设置自中断时间
harakiri=30
# 设置缓冲
post-buffering=4096