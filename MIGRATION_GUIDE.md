# Flask到FastAPI迁移指南

## 概述
本项目已从Flask框架迁移到FastAPI框架，并从uWSGI部署方式改为Gunicorn + Uvicorn Worker部署方式。

## 主要变更

### 1. 框架变更
- **原框架**: Flask + uWSGI
- **新框架**: FastAPI + Gunicorn + Uvicorn Worker

### 2. 文件变更

#### 新增文件
- `main.py` - FastAPI主应用文件（替代server.py）
- `requirements.txt` - 项目依赖文件
- `gunicorn.conf.py` - Gunicorn配置文件
- `start.sh` - 启动脚本
- `stop.sh` - 停止脚本
- `MIGRATION_GUIDE.md` - 本迁移指南

#### 修改文件
- `log.py` - 添加FastAPI日志支持
- `core/taskScheduler.py` - 从Flask-APScheduler迁移到APScheduler

#### 保留文件
- `server.py` - 保留原Flask版本（可选择删除）
- `uwsgi.ini` - 保留原uWSGI配置（可选择删除）

### 3. 依赖变更

#### 新增依赖
```
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
python-multipart==0.0.6
jinja2==3.1.2
aiofiles==23.2.1
apscheduler==3.10.4
```

#### 移除依赖
- flask
- flask-apscheduler

### 4. 功能对比

| 功能 | Flask版本 | FastAPI版本 | 状态 |
|------|-----------|-------------|------|
| 路由处理 | @app.route | @app.api_route | ✅ 已迁移 |
| 请求参数处理 | request.postvars | request.state.postvars | ✅ 已迁移 |
| 中间件 | @app.before_request/@app.after_request | @app.middleware("http") | ✅ 已迁移 |
| 静态文件服务 | send_from_directory | StaticFiles | ✅ 已迁移 |
| 日志记录 | Flask Logger | Python logging | ✅ 已迁移 |
| 定时任务 | Flask-APScheduler | APScheduler | ✅ 已迁移 |
| 跨域支持 | - | CORSMiddleware | ✅ 新增 |

## 部署说明

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动应用

#### 开发环境
```bash
# 直接运行
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 9083 --reload
```

#### 生产环境
```bash
# 使用启动脚本
chmod +x start.sh
./start.sh

# 或直接使用gunicorn
gunicorn main:app -c gunicorn.conf.py
```

### 3. 停止应用
```bash
# 使用停止脚本
chmod +x stop.sh
./stop.sh

# 或手动停止
pkill -f "gunicorn main:app"
```

### 4. 日志文件
- 应用日志: `./logs/app.log`
- Gunicorn访问日志: `./logs/gunicorn_access.log`
- Gunicorn错误日志: `./logs/gunicorn_error.log`
- 启动日志: `./logs/startup.log`

## 配置说明

### Gunicorn配置 (gunicorn.conf.py)
- **workers**: 5个工作进程（对应原uWSGI配置）
- **worker_class**: uvicorn.workers.UvicornWorker
- **timeout**: 30秒（对应原uWSGI的harakiri）
- **bind**: 0.0.0.0:9083（保持原端口）

### 性能对比
- **并发处理**: FastAPI支持异步处理，理论上性能更好
- **内存使用**: 相比Flask+uWSGI，内存使用可能略有不同
- **启动时间**: FastAPI启动时间通常更快

## 兼容性说明

### API兼容性
- 所有原有API端点保持不变
- 请求和响应格式完全兼容
- 前端代码无需修改

### 功能兼容性
- 定时任务功能保持不变
- 数据库连接池保持不变
- 日志记录格式保持不变
- 静态文件服务保持不变

## 注意事项

1. **异步支持**: FastAPI原生支持异步，但当前迁移保持同步方式以确保兼容性
2. **错误处理**: FastAPI的错误处理机制与Flask略有不同，但已做适配
3. **中间件**: 请求处理中间件已完全迁移Flask的before_request和after_request功能
4. **定时任务**: 从Flask-APScheduler迁移到标准APScheduler，功能保持一致

## 回滚方案

如需回滚到Flask版本：
1. 停止FastAPI应用: `./stop.sh`
2. 启动Flask应用: `nohup uwsgi uwsgi.ini >> ./logs/flask.log 2>&1 &`
3. 原Flask代码在`server.py`中保持不变

## 测试建议

1. **功能测试**: 测试所有API端点是否正常工作
2. **性能测试**: 对比迁移前后的性能表现
3. **稳定性测试**: 长时间运行测试，观察内存和CPU使用情况
4. **定时任务测试**: 确认定时任务正常执行

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 依赖是否正确安装
3. 端口是否被占用
4. 权限是否正确设置
