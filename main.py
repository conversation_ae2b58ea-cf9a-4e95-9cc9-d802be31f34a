import os
import time
import logging
from typing import Dict, Any

from fastapi import FastAPI, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from conf.schedulerConfig import schedulerConfig
from core.service import *
from core.taskScheduler import scheduler
from log import Logger

# 全局变量
root = os.path.dirname(os.path.abspath(__file__))

# 服务实例（延迟初始化）
polySrv = None
shopSrv = None
orderSrv = None
goodsSrv = None
recordSrv = None
platSrv = None
commonSrv = None

def init_services():
    """初始化服务实例"""
    global polySrv, shopSrv, orderSrv, goodsSrv, recordSrv, platSrv, commonSrv
    try:
        polySrv = polyService()
        shopSrv = shopService()
        orderSrv = orderService()
        goodsSrv = goodsService()
        recordSrv = recordService()
        platSrv = platService()
        commonSrv = commonExampleService()
        logging.info("所有服务初始化完成")
        return True
    except Exception as e:
        logging.error(f"服务初始化失败: {str(e)}")
        return False

# 创建FastAPI应用
app = FastAPI(
    title="模拟网关后台",
    description="Flask到FastAPI迁移版本",
    version="2.0.0"
)

# 应用启动时初始化
@app.on_event("startup")
async def startup_event():
    try:
        # 初始化logger
        logger_instance = Logger()
        logger_instance.init_fastapi_app()
        logging.info("日志系统初始化完成")

        # 初始化服务
        init_services()

        # 启动定时任务
        scheduler.start()
        logging.info("定时任务已启动")

    except Exception as e:
        logging.error(f"应用启动时发生错误: {str(e)}")

# 应用关闭时清理
@app.on_event("shutdown")
async def shutdown_event():
    try:
        scheduler.shutdown()
        logging.info("定时任务已关闭")
    except Exception as e:
        logging.error(f"应用关闭时发生错误: {str(e)}")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    
    # 记录请求开始
    logging.info(f'[{request.method}]{request.url.path}?{request.url.query} -- (from={request.client.host})')
    
    # 处理请求参数
    request.state.postvars = await get_request_vars(request)
    request.state.postvars['remote_addr'] = request.client.host
    request.state.postvars['_timestamp'] = timestamp
    
    response = await call_next(request)
    
    # 记录请求结束
    process_time = time.time() - start_time
    logging.info(f'[{request.method}]{request.url.path}?{request.url.query} -- {response.status_code} (from={request.client.host},{timestamp}) - {process_time:.3f}s')
    
    return response

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 请求参数整合函数
async def get_request_vars(request: Request) -> Dict[str, Any]:
    postvars = {}
    
    # 获取查询参数
    if request.query_params:
        postvars.update(dict(request.query_params))
    
    # 获取表单数据
    if request.method == "POST":
        try:
            if request.headers.get("content-type", "").startswith("application/x-www-form-urlencoded"):
                form_data = await request.form()
                postvars.update(dict(form_data))
            elif request.headers.get("content-type", "").startswith("application/json"):
                json_data = await request.json()
                if isinstance(json_data, dict):
                    postvars.update(json_data)
        except Exception:
            pass
    
    # key转小写
    newDic = {}
    for key, value in postvars.items():
        if isinstance(value, list) and len(value) > 0:
            newDic[key.lower()] = value[0]
        else:
            newDic[key.lower()] = str(value) if value is not None else ""
    
    return newDic

# 路由定义
@app.get("/")
async def index():
    return FileResponse(os.path.join(root, "index.html"))

@app.get("/log")
async def log_page():
    return FileResponse(os.path.join(root, "log.html"))

@app.get("/health")
async def health_check():
    """健康检查端点"""
    services_status = {
        "polySrv": polySrv is not None,
        "shopSrv": shopSrv is not None,
        "orderSrv": orderSrv is not None,
        "goodsSrv": goodsSrv is not None,
        "recordSrv": recordSrv is not None,
        "platSrv": platSrv is not None,
        "commonSrv": commonSrv is not None,
    }

    all_services_ready = all(services_status.values())

    return {
        "status": "ok" if all_services_ready else "partial",
        "message": "FastAPI应用运行正常" if all_services_ready else "部分服务未就绪",
        "services": services_status,
        "framework": "FastAPI",
        "version": "2.0.0"
    }

# 通用API路由
@app.api_route("/common/getapi", methods=["GET", "POST"])
async def get_api(request: Request):
    if polySrv is None:
        return JSONResponse(content={"error": "服务未就绪"}, status_code=503)
    return JSONResponse(content=polySrv.getApiAll())

@app.api_route("/polymsg/getApiByType", methods=["GET", "POST"])
async def get_api_by_type(request: Request):
    return JSONResponse(content=polySrv.getApiByType(request.state.postvars))

@app.api_route("/polymsg/getById", methods=["GET", "POST"])
async def get_api_detail(request: Request):
    return JSONResponse(content=polySrv.getApiDetail(request.state.postvars))

@app.api_route("/openapi/do", methods=["GET", "POST"])
async def open_api(request: Request):
    return JSONResponse(content=polySrv.doBusiness(request.state.postvars))

# 记录相关路由
@app.api_route("/record/get", methods=["GET", "POST"])
async def get_record(request: Request):
    return JSONResponse(content=recordSrv.getRecords(request.state.postvars))

@app.api_route("/record/detail", methods=["GET", "POST"])
async def get_record_detail(request: Request):
    return JSONResponse(content=recordSrv.getRecordDetail(request.state.postvars))

# 订单相关路由
@app.api_route("/order/get", methods=["GET", "POST"])
async def get_order(request: Request):
    return JSONResponse(content=orderSrv.getExample(request.state.postvars))

@app.api_route("/order/add", methods=["GET", "POST"])
async def add_order(request: Request):
    return JSONResponse(content=orderSrv.addExample(request.state.postvars))

@app.api_route("/order/update", methods=["GET", "POST"])
async def update_order(request: Request):
    return JSONResponse(content=orderSrv.updateExample(request.state.postvars))

@app.api_route("/order/del", methods=["GET", "POST"])
async def delete_order(request: Request):
    return JSONResponse(content=orderSrv.delExample(request.state.postvars))

@app.api_route("/order/getById", methods=["GET", "POST"])
async def get_order_by_id(request: Request):
    return JSONResponse(content=orderSrv.getById(request.state.postvars))

@app.api_route("/order/push", methods=["GET", "POST"])
async def push_order(request: Request):
    return JSONResponse(content=orderSrv.pushByExample(request.state.postvars))

@app.api_route("/order/pushMany", methods=["GET", "POST"])
async def push_many_order(request: Request):
    return JSONResponse(content=orderSrv.pushByExampleMany(request.state.postvars))

@app.api_route("/order/pushNotice", methods=["GET", "POST"])
async def push_notice(request: Request):
    return JSONResponse(content=orderSrv.pushNotice(request.state.postvars))

@app.api_route("/order/make", methods=["GET", "POST"])
async def make_order(request: Request):
    return JSONResponse(content=orderSrv.makeOrder(request.state.postvars))

@app.api_route("/order/openapipush", methods=["GET", "POST"])
async def openapi_push(request: Request):
    return JSONResponse(content=orderSrv.pushByExample(request.state.postvars))

# 店铺相关路由
@app.api_route("/shop/getShopByPlat", methods=["GET", "POST"])
async def shop(request: Request):
    return JSONResponse(content=shopSrv.getShopList(request.state.postvars))

# 平台相关路由
@app.api_route("/plat/getall", methods=["GET", "POST"])
async def get_plats(request: Request):
    return JSONResponse(content=platSrv.getPlatList())

@app.api_route("/plat/add", methods=["GET", "POST"])
async def add_plat(request: Request):
    return JSONResponse(content=platSrv.addPlat(request.state.postvars))

# 商品相关路由
@app.api_route("/goods/get", methods=["GET", "POST"])
async def get_goods(request: Request):
    return JSONResponse(content=goodsSrv.getGoodsBrief(request.state.postvars))

@app.api_route("/goods/getById", methods=["GET", "POST"])
async def get_goods_by_id(request: Request):
    return JSONResponse(content=goodsSrv.getById(request.state.postvars))

@app.api_route("/goods/add", methods=["GET", "POST"])
async def add_goods(request: Request):
    return JSONResponse(content=goodsSrv.addGoods(request.state.postvars))

@app.api_route("/goods/update", methods=["GET", "POST"])
async def update_goods(request: Request):
    return JSONResponse(content=goodsSrv.updateGoods(request.state.postvars))

@app.api_route("/goods/del", methods=["GET", "POST"])
async def del_goods(request: Request):
    return JSONResponse(content=goodsSrv.delGoods(request.state.postvars))

# 通用样例相关路由
@app.api_route("/common/get", methods=["GET", "POST"])
async def get_common(request: Request):
    return JSONResponse(content=commonSrv.getExample(request.state.postvars))

@app.api_route("/common/getById", methods=["GET", "POST"])
async def get_common_by_id(request: Request):
    return JSONResponse(content=commonSrv.getById(request.state.postvars))

@app.api_route("/common/add", methods=["GET", "POST"])
async def add_common(request: Request):
    return JSONResponse(content=commonSrv.addExample(request.state.postvars))

@app.api_route("/common/update", methods=["GET", "POST"])
async def update_common(request: Request):
    return JSONResponse(content=commonSrv.updateExample(request.state.postvars))

@app.api_route("/common/del", methods=["GET", "POST"])
async def del_common(request: Request):
    return JSONResponse(content=commonSrv.delExample(request.state.postvars))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9083)
