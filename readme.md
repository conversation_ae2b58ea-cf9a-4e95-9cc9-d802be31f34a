### 默认端口

9083

### 依赖包

+ flask
+ bs4
+ pymysql
+ numpy
+ requests
+ lxml
+ dbutils


### 依赖包安装(指定安装源)

pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask bs4 pymysql numpy requests lxml dbutils


### centos7 部署流程参考

https://blog.csdn.net/u010067848/article/details/84826340



### uwsgi启动:

##### 输出到flask.log
nohup uwsgi uwsgi.ini >> ./log/flask.log 2>&1 &
##### 输出到黑洞
nohup uwsgi uwsgi.ini >/dev/null 2>&1 &



### uwsgi 关闭：
killall -9 uwsgi

nohup java -jar media-1.1.0.jar >/dev/null 2>&1 &