import os
import time
import logging
from typing import Dict, Any

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# 初始化日志
from log import Logger
logger_instance = Logger()
logger_instance.init_fastapi_app()
logging.info("日志系统初始化完成")

# 根目录
root = os.path.dirname(os.path.abspath(__file__))

# 创建FastAPI应用
app = FastAPI(
    title="模拟网关后台",
    description="Flask到FastAPI迁移版本 - 最小版",
    version="2.0.0-minimal"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    
    # 记录请求开始
    logging.info(f'[{request.method}]{request.url.path}?{request.url.query} -- (from={request.client.host})')
    
    # 处理请求参数
    request.state.postvars = await get_request_vars(request)
    request.state.postvars['remote_addr'] = request.client.host
    request.state.postvars['_timestamp'] = timestamp
    
    response = await call_next(request)
    
    # 记录请求结束
    process_time = time.time() - start_time
    logging.info(f'[{request.method}]{request.url.path}?{request.url.query} -- {response.status_code} (from={request.client.host},{timestamp}) - {process_time:.3f}s')
    
    return response

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 请求参数整合函数
async def get_request_vars(request: Request) -> Dict[str, Any]:
    postvars = {}
    
    # 获取查询参数
    if request.query_params:
        postvars.update(dict(request.query_params))
    
    # 获取表单数据
    if request.method == "POST":
        try:
            if request.headers.get("content-type", "").startswith("application/x-www-form-urlencoded"):
                form_data = await request.form()
                postvars.update(dict(form_data))
            elif request.headers.get("content-type", "").startswith("application/json"):
                json_data = await request.json()
                if isinstance(json_data, dict):
                    postvars.update(json_data)
        except Exception:
            pass
    
    # key转小写
    newDic = {}
    for key, value in postvars.items():
        if isinstance(value, list) and len(value) > 0:
            newDic[key.lower()] = value[0]
        else:
            newDic[key.lower()] = str(value) if value is not None else ""
    
    return newDic

# 路由定义
@app.get("/")
async def index():
    return FileResponse(os.path.join(root, "index.html"))

@app.get("/log")
async def log_page():
    return FileResponse(os.path.join(root, "log.html"))

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "ok",
        "message": "FastAPI应用运行正常",
        "framework": "FastAPI",
        "version": "2.0.0-minimal"
    }

@app.get("/test")
async def test():
    return {"message": "测试接口正常", "framework": "FastAPI", "version": "2.0.0-minimal"}

# 延迟加载服务的路由
@app.api_route("/common/getapi", methods=["GET", "POST"])
async def get_api():
    try:
        from core.service import polyService
        polySrv = polyService()
        return JSONResponse(content=polySrv.getApiAll())
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/polymsg/getApiByType", methods=["GET", "POST"])
async def get_api_by_type(request: Request):
    try:
        from core.service import polyService
        polySrv = polyService()
        return JSONResponse(content=polySrv.getApiByType(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/polymsg/getById", methods=["GET", "POST"])
async def get_api_detail(request: Request):
    try:
        from core.service import polyService
        polySrv = polyService()
        return JSONResponse(content=polySrv.getApiDetail(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/openapi/do", methods=["GET", "POST"])
async def open_api(request: Request):
    try:
        from core.service import polyService
        polySrv = polyService()
        return JSONResponse(content=polySrv.doBusiness(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 记录相关路由
@app.api_route("/record/get", methods=["GET", "POST"])
async def get_record(request: Request):
    try:
        from core.service import recordService
        recordSrv = recordService()
        return JSONResponse(content=recordSrv.getRecords(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/record/detail", methods=["GET", "POST"])
async def get_record_detail(request: Request):
    try:
        from core.service import recordService
        recordSrv = recordService()
        return JSONResponse(content=recordSrv.getRecordDetail(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 订单相关路由
@app.api_route("/order/get", methods=["GET", "POST"])
async def get_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.getExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/add", methods=["GET", "POST"])
async def add_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.addExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/update", methods=["GET", "POST"])
async def update_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.updateExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/del", methods=["GET", "POST"])
async def delete_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.delExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/getById", methods=["GET", "POST"])
async def get_order_by_id(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.getById(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/push", methods=["GET", "POST"])
async def push_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.pushByExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/pushMany", methods=["GET", "POST"])
async def push_many_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.pushByExampleMany(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/pushNotice", methods=["GET", "POST"])
async def push_notice(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.pushNotice(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/make", methods=["GET", "POST"])
async def make_order(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.makeOrder(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/order/openapipush", methods=["GET", "POST"])
async def openapi_push(request: Request):
    try:
        from core.service import orderService
        orderSrv = orderService()
        return JSONResponse(content=orderSrv.pushByExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 店铺相关路由
@app.api_route("/shop/getShopByPlat", methods=["GET", "POST"])
async def shop(request: Request):
    try:
        from core.service import shopService
        shopSrv = shopService()
        return JSONResponse(content=shopSrv.getShopList(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 平台相关路由
@app.api_route("/plat/getall", methods=["GET", "POST"])
async def get_plats():
    try:
        from core.service import platService
        platSrv = platService()
        return JSONResponse(content=platSrv.getPlatList())
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/plat/add", methods=["GET", "POST"])
async def add_plat(request: Request):
    try:
        from core.service import platService
        platSrv = platService()
        return JSONResponse(content=platSrv.addPlat(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 商品相关路由
@app.api_route("/goods/get", methods=["GET", "POST"])
async def get_goods(request: Request):
    try:
        from core.service import goodsService
        goodsSrv = goodsService()
        return JSONResponse(content=goodsSrv.getGoodsBrief(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/getById", methods=["GET", "POST"])
async def get_goods_by_id(request: Request):
    try:
        from core.service import goodsService
        goodsSrv = goodsService()
        return JSONResponse(content=goodsSrv.getById(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/add", methods=["GET", "POST"])
async def add_goods(request: Request):
    try:
        from core.service import goodsService
        goodsSrv = goodsService()
        return JSONResponse(content=goodsSrv.addGoods(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/update", methods=["GET", "POST"])
async def update_goods(request: Request):
    try:
        from core.service import goodsService
        goodsSrv = goodsService()
        return JSONResponse(content=goodsSrv.updateGoods(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/goods/del", methods=["GET", "POST"])
async def del_goods(request: Request):
    try:
        from core.service import goodsService
        goodsSrv = goodsService()
        return JSONResponse(content=goodsSrv.delGoods(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

# 通用样例相关路由
@app.api_route("/common/get", methods=["GET", "POST"])
async def get_common(request: Request):
    try:
        from core.service import commonExampleService
        commonSrv = commonExampleService()
        return JSONResponse(content=commonSrv.getExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/getById", methods=["GET", "POST"])
async def get_common_by_id(request: Request):
    try:
        from core.service import commonExampleService
        commonSrv = commonExampleService()
        return JSONResponse(content=commonSrv.getById(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/add", methods=["GET", "POST"])
async def add_common(request: Request):
    try:
        from core.service import commonExampleService
        commonSrv = commonExampleService()
        return JSONResponse(content=commonSrv.addExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/update", methods=["GET", "POST"])
async def update_common(request: Request):
    try:
        from core.service import commonExampleService
        commonSrv = commonExampleService()
        return JSONResponse(content=commonSrv.updateExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

@app.api_route("/common/del", methods=["GET", "POST"])
async def del_common(request: Request):
    try:
        from core.service import commonExampleService
        commonSrv = commonExampleService()
        return JSONResponse(content=commonSrv.delExample(request.state.postvars))
    except Exception as e:
        logging.error(f"服务调用失败: {str(e)}")
        return JSONResponse(content={"error": f"服务调用失败: {str(e)}"}, status_code=500)

if __name__ == "__main__":
    import uvicorn
    print("启动FastAPI最小版应用...")
    uvicorn.run(app, host="0.0.0.0", port=9083, log_level="info")
