<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏轮播iframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        #frameContainer {
            width: 100vw;
            height: 100vh;
        }
        
        #contentFrame {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div id="frameContainer">
        <iframe id="contentFrame" src="about:blank"></iframe>
    </div>

    <script>
        // 预定义的URL列表
        const urls = [
            // 'https://api.jackyun.com/oms-online/apibigscreen/build/#/index',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/catchorder',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/timeconsume',
            //'https://api.jackyun.com/oms-online/apibigscreen/build/#/paidui',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/delivery',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/syncinventory',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/message',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/downloadorderandsave',
            'https://api.jackyun.com/oms-online/apibigscreen/build/#/downloadorderqueue'
        ];
        
        let currentIndex = 0;
        const frame = document.getElementById('contentFrame');

        // URL切换函数
        function switchUrl() {
            frame.src = urls[currentIndex];
            currentIndex = (currentIndex + 1) % urls.length;
        }

        // 初始化定时器
        document.addEventListener('DOMContentLoaded', function() {
            // 立即加载第一个URL
            switchUrl();
            
            // 设置定时切换（30秒）
            setInterval(switchUrl, 30000);
        });

        // 错误处理（可选）
        frame.addEventListener('error', function(e) {
            console.error('iframe加载错误:', e);
        });
    </script>
</body>
</html>