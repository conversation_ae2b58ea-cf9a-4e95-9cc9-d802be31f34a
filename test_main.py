import os
import time
import logging
from typing import Dict, Any

from fastapi import FastAPI, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# 简化版本的FastAPI应用，用于测试基本功能
app = FastAPI(
    title="模拟网关后台 - 测试版",
    description="Flask到FastAPI迁移测试版本",
    version="2.0.0-test"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根目录
root = os.path.dirname(os.path.abspath(__file__))

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    
    # 记录请求开始
    print(f'[{request.method}]{request.url.path}?{request.url.query} -- (from={request.client.host})')
    
    response = await call_next(request)
    
    # 记录请求结束
    process_time = time.time() - start_time
    print(f'[{request.method}]{request.url.path}?{request.url.query} -- {response.status_code} (from={request.client.host},{timestamp}) - {process_time:.3f}s')
    
    return response

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 基本路由
@app.get("/")
async def index():
    return FileResponse(os.path.join(root, "index.html"))

@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "FastAPI应用运行正常"}

@app.get("/test")
async def test():
    return {"message": "测试接口正常", "framework": "FastAPI", "version": "2.0.0-test"}

if __name__ == "__main__":
    import uvicorn
    print("启动FastAPI测试应用...")
    uvicorn.run(app, host="0.0.0.0", port=9083, log_level="info")
